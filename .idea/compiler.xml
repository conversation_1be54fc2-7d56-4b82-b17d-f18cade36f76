<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="blind-date" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="blind-date-business" target="1.8" />
      <module name="blind-date-common" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="blind-date" options="-parameters" />
      <module name="blind-date-business" options="-parameters" />
      <module name="blind-date-common" options="-parameters" />
    </option>
  </component>
</project>