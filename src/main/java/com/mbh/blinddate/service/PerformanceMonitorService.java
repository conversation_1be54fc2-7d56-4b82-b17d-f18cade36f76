package com.mbh.blinddate.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/**
 * 性能监控服务
 * 提供系统性能指标监控
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class PerformanceMonitorService {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private MeterRegistry meterRegistry;

    private final Counter loginCounter;
    private final Counter registerCounter;
    private final Timer databaseTimer;

    public PerformanceMonitorService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.loginCounter = Counter.builder("user.login.count")
                .description("用户登录次数")
                .register(meterRegistry);
        this.registerCounter = Counter.builder("user.register.count")
                .description("用户注册次数")
                .register(meterRegistry);
        this.databaseTimer = Timer.builder("database.query.time")
                .description("数据库查询时间")
                .register(meterRegistry);
    }

    /**
     * 记录用户登录
     */
    public void recordLogin() {
        loginCounter.increment();
    }

    /**
     * 记录用户注册
     */
    public void recordRegister() {
        registerCounter.increment();
    }

    /**
     * 记录数据库查询时间
     */
    public Timer.Sample startDatabaseTimer() {
        return Timer.start(meterRegistry);
    }

    /**
     * 获取系统性能指标
     */
    public Map<String, Object> getPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        // 数据库连接池状态
        try (Connection connection = dataSource.getConnection()) {
            metrics.put("database_connected", true);
            metrics.put("database_url", connection.getMetaData().getURL());
        } catch (Exception e) {
            metrics.put("database_connected", false);
            metrics.put("database_error", e.getMessage());
        }
        
        // 缓存统计
        try {
            if (cacheManager instanceof org.springframework.cache.caffeine.CaffeineCacheManager) {
                org.springframework.cache.caffeine.CaffeineCacheManager caffeineCacheManager = 
                    (org.springframework.cache.caffeine.CaffeineCacheManager) cacheManager;
                
                Map<String, Object> cacheStats = new HashMap<>();
                caffeineCacheManager.getCacheNames().forEach(cacheName -> {
                    org.springframework.cache.caffeine.CaffeineCache cache = 
                        (org.springframework.cache.caffeine.CaffeineCache) caffeineCacheManager.getCache(cacheName);
                    if (cache != null) {
                        com.github.benmanes.caffeine.cache.stats.CacheStats stats = 
                            cache.getNativeCache().stats();
                        Map<String, Object> stat = new HashMap<>();
                        stat.put("hitCount", stats.hitCount());
                        stat.put("missCount", stats.missCount());
                        stat.put("hitRate", stats.hitRate());
                        stat.put("evictionCount", stats.evictionCount());
                        cacheStats.put(cacheName, stat);
                    }
                });
                metrics.put("cache_stats", cacheStats);
            }
        } catch (Exception e) {
            log.warn("获取缓存统计失败: {}", e.getMessage());
        }
        
        // JVM内存信息
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> memory = new HashMap<>();
        memory.put("total_mb", runtime.totalMemory() / 1024 / 1024);
        memory.put("free_mb", runtime.freeMemory() / 1024 / 1024);
        memory.put("used_mb", (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024);
        memory.put("max_mb", runtime.maxMemory() / 1024 / 1024);
        metrics.put("memory", memory);
        
        // 线程信息
        metrics.put("active_threads", Thread.activeCount());
        
        // 自定义指标
        metrics.put("login_count", loginCounter.count());
        metrics.put("register_count", registerCounter.count());
        
        return metrics;
    }
}
