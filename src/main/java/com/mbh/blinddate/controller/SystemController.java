package com.mbh.blinddate.controller;

import com.mbh.blinddate.common.Result;
import com.mbh.blinddate.config.properties.AppProperties;
import com.mbh.blinddate.service.PerformanceMonitorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统监控控制器
 * 
 * <AUTHOR>
 */
@Tag(name = "系统监控", description = "系统状态监控相关接口")
@RestController
@RequestMapping("/system")
public class SystemController {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private PerformanceMonitorService performanceMonitorService;

    @Autowired
    private AppProperties appProperties;

    @Operation(summary = "系统健康检查", description = "检查数据库和Redis连接状态")
    @GetMapping("/health")
    public Result<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        // 检查数据库连接
        try (Connection connection = dataSource.getConnection()) {
            health.put("database", "connected");
            health.put("database_url", connection.getMetaData().getURL());
            health.put("database_driver", connection.getMetaData().getDriverName());
        } catch (Exception e) {
            health.put("database", "disconnected");
            health.put("database_error", e.getMessage());
        }
        
        // 检查Redis连接
        try {
            redisTemplate.opsForValue().set("health_check", "ok");
            String value = (String) redisTemplate.opsForValue().get("health_check");
            if ("ok".equals(value)) {
                health.put("redis", "connected");
            } else {
                health.put("redis", "error");
            }
            redisTemplate.delete("health_check");
        } catch (Exception e) {
            health.put("redis", "disconnected");
            health.put("redis_error", e.getMessage());
        }
        
        health.put("status", "running");
        health.put("timestamp", System.currentTimeMillis());
        
        return Result.success(health);
    }

    @Operation(summary = "系统信息", description = "获取系统基本信息")
    @GetMapping("/info")
    public Result<Map<String, Object>> systemInfo() {
        Map<String, Object> info = new HashMap<>();
        
        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        info.put("java_version", System.getProperty("java.version"));
        info.put("java_vendor", System.getProperty("java.vendor"));
        info.put("os_name", System.getProperty("os.name"));
        info.put("os_version", System.getProperty("os.version"));
        
        // 内存信息
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        
        Map<String, Object> memory = new HashMap<>();
        memory.put("total", totalMemory / 1024 / 1024 + " MB");
        memory.put("free", freeMemory / 1024 / 1024 + " MB");
        memory.put("used", (totalMemory - freeMemory) / 1024 / 1024 + " MB");
        memory.put("max", maxMemory / 1024 / 1024 + " MB");
        
        info.put("memory", memory);
        info.put("processors", runtime.availableProcessors());
        info.put("timestamp", System.currentTimeMillis());
        
        return Result.success(info);
    }

    @Operation(summary = "性能监控", description = "获取系统性能监控指标")
    @GetMapping("/performance")
    public Result<Map<String, Object>> performanceMetrics() {
        Map<String, Object> metrics = performanceMonitorService.getPerformanceMetrics();
        return Result.success(metrics);
    }

    @Operation(summary = "缓存统计", description = "获取缓存使用统计信息")
    @GetMapping("/cache-stats")
    public Result<Map<String, Object>> cacheStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 这里可以添加更详细的缓存统计逻辑
            stats.put("cache_enabled", true);
            stats.put("cache_type", "caffeine");
            stats.put("timestamp", System.currentTimeMillis());
        } catch (Exception e) {
            stats.put("cache_enabled", false);
            stats.put("error", e.getMessage());
        }

        return Result.success(stats);
    }

    @Operation(summary = "应用配置信息", description = "获取当前应用的配置参数")
    @GetMapping("/config")
    public Result<Map<String, Object>> configInfo() {
        Map<String, Object> config = new HashMap<>();

        // 异步任务配置
        Map<String, Object> asyncConfig = new HashMap<>();
        asyncConfig.put("task", appProperties.getAsync().getTask());
        asyncConfig.put("email", appProperties.getAsync().getEmail());
        asyncConfig.put("file", appProperties.getAsync().getFile());
        config.put("async", asyncConfig);

        // 缓存配置
        Map<String, Object> cacheConfig = new HashMap<>();
        cacheConfig.put("caffeine", appProperties.getCache().getCaffeine());
        cacheConfig.put("user", appProperties.getCache().getUser());
        cacheConfig.put("config", appProperties.getCache().getConfig());
        config.put("cache", cacheConfig);

        // 事务配置
        config.put("transaction", appProperties.getTransaction());

        config.put("timestamp", System.currentTimeMillis());

        return Result.success(config);
    }
}
