package com.mbh.blinddate.config;

import com.mbh.blinddate.config.properties.AsyncProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务配置类
 * 替代默认的SimpleAsyncTaskExecutor，提供线程池复用
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {

    private final AsyncProperties asyncProperties;

    public AsyncConfig(AsyncProperties asyncProperties) {
        this.asyncProperties = asyncProperties;
    }

    /**
     * 异步任务执行器
     */
    @Bean("taskExecutor")
    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        AsyncProperties.TaskPool taskPool = asyncProperties.getTask();

        // 从配置属性读取参数
        executor.setCorePoolSize(taskPool.getCorePoolSize());
        executor.setMaxPoolSize(taskPool.getMaxPoolSize());
        executor.setQueueCapacity(taskPool.getQueueCapacity());
        executor.setKeepAliveSeconds(taskPool.getKeepAliveSeconds());
        executor.setThreadNamePrefix("async-task-");

        // 拒绝策略：由调用线程处理该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(taskPool.getAwaitTerminationSeconds());

        executor.initialize();
        return executor;
    }

    /**
     * 邮件发送异步执行器
     */
    @Bean("emailExecutor")
    public Executor emailExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        AsyncProperties.EmailPool emailPool = asyncProperties.getEmail();
        AsyncProperties.TaskPool taskPool = asyncProperties.getTask();

        executor.setCorePoolSize(emailPool.getCorePoolSize());
        executor.setMaxPoolSize(emailPool.getMaxPoolSize());
        executor.setQueueCapacity(emailPool.getQueueCapacity());
        executor.setKeepAliveSeconds(taskPool.getKeepAliveSeconds());
        executor.setThreadNamePrefix("email-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(taskPool.getAwaitTerminationSeconds());
        executor.initialize();
        return executor;
    }

    /**
     * 文件处理异步执行器
     */
    @Bean("fileExecutor")
    public Executor fileExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        AsyncProperties.FilePool filePool = asyncProperties.getFile();
        AsyncProperties.TaskPool taskPool = asyncProperties.getTask();

        executor.setCorePoolSize(filePool.getCorePoolSize());
        executor.setMaxPoolSize(filePool.getMaxPoolSize());
        executor.setQueueCapacity(filePool.getQueueCapacity());
        executor.setKeepAliveSeconds(taskPool.getKeepAliveSeconds());
        executor.setThreadNamePrefix("file-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(taskPool.getAwaitTerminationSeconds());
        executor.initialize();
        return executor;
    }

    /**
     * 异步任务异常处理器
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> {
            log.error("异步任务执行异常 - 方法: {}, 参数: {}, 异常: {}", 
                    method.getName(), params, ex.getMessage(), ex);
        };
    }
}
