package com.mbh.blinddate.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * 缓存配置类
 * 使用Caffeine替代默认的ConcurrentHashMap，提供过期机制
 * 
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class CacheConfig {

    // 通用缓存配置
    @Value("${cache.caffeine.maximum-size:10000}")
    private long maximumSize;

    @Value("${cache.caffeine.expire-after-write-minutes:10}")
    private long expireAfterWriteMinutes;

    @Value("${cache.caffeine.expire-after-access-minutes:5}")
    private long expireAfterAccessMinutes;

    @Value("${cache.caffeine.initial-capacity:100}")
    private int initialCapacity;

    // 用户缓存配置
    @Value("${cache.user.maximum-size:5000}")
    private long userMaximumSize;

    @Value("${cache.user.expire-after-write-minutes:5}")
    private long userExpireAfterWriteMinutes;

    // 配置缓存配置
    @Value("${cache.config.maximum-size:1000}")
    private long configMaximumSize;

    @Value("${cache.config.expire-after-write-hours:1}")
    private long configExpireAfterWriteHours;

    /**
     * 配置Caffeine缓存管理器
     */
    @Bean
    @Primary
    public CacheManager caffeineCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        // 配置缓存属性 - 从配置文件读取
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterWrite(expireAfterWriteMinutes, TimeUnit.MINUTES)
                .expireAfterAccess(expireAfterAccessMinutes, TimeUnit.MINUTES)
                .initialCapacity(initialCapacity)
                .recordStats());

        // 设置缓存名称
        cacheManager.setCacheNames(java.util.Arrays.asList(
                "userCache",      // 用户缓存
                "tokenCache",     // Token缓存
                "configCache",    // 配置缓存
                "matchCache",     // 匹配缓存
                "messageCache"    // 消息缓存
        ));

        return cacheManager;
    }

    /**
     * 用户信息缓存 - 短期缓存
     */
    @Bean("userCacheManager")
    public CacheManager userCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager("userCache");
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(userMaximumSize)
                .expireAfterWrite(userExpireAfterWriteMinutes, TimeUnit.MINUTES)
                .recordStats());
        return cacheManager;
    }

    /**
     * 配置信息缓存 - 长期缓存
     */
    @Bean("configCacheManager")
    public CacheManager configCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager("configCache");
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(configMaximumSize)
                .expireAfterWrite(configExpireAfterWriteHours, TimeUnit.HOURS)
                .recordStats());
        return cacheManager;
    }
}
