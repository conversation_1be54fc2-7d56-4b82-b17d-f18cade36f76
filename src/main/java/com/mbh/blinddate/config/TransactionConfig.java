package com.mbh.blinddate.config;

import com.mbh.blinddate.config.properties.TransactionProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.sql.DataSource;

/**
 * 事务配置类
 * 优化事务管理，避免长事务持锁影响并发
 * 
 * <AUTHOR>
 */
@Configuration
@EnableTransactionManagement
public class TransactionConfig implements TransactionManagementConfigurer {

    private final DataSource dataSource;
    private final TransactionProperties transactionProperties;

    public TransactionConfig(DataSource dataSource, TransactionProperties transactionProperties) {
        this.dataSource = dataSource;
        this.transactionProperties = transactionProperties;
    }

    @Bean
    @Override
    public PlatformTransactionManager annotationDrivenTransactionManager() {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(dataSource);
        // 从配置属性读取事务超时时间
        transactionManager.setDefaultTimeout(transactionProperties.getDefaultTimeout());
        // 设置事务同步
        transactionManager.setGlobalRollbackOnParticipationFailure(
                transactionProperties.isGlobalRollbackOnParticipationFailure());
        return transactionManager;
    }
}
