package com.mbh.blinddate.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 应用配置总览
 * 统一管理所有app前缀下的配置
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "app")
public class AppProperties {

    /**
     * 异步任务配置
     */
    private AsyncConfig async = new AsyncConfig();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 事务配置
     */
    private TransactionConfig transaction = new TransactionConfig();


    @Data
    public static class AsyncConfig {
        private TaskPool task = new TaskPool();
        private EmailPool email = new EmailPool();
        private FilePool file = new FilePool();

        @Data
        public static class TaskPool {
            private int corePoolSize = 8;
            private int maxPoolSize = 16;
            private int queueCapacity = 100;
            private int keepAliveSeconds = 60;
            private int awaitTerminationSeconds = 60;
        }

        @Data
        public static class EmailPool {
            private int corePoolSize = 2;
            private int maxPoolSize = 5;
            private int queueCapacity = 50;
        }

        @Data
        public static class FilePool {
            private int corePoolSize = 4;
            private int maxPoolSize = 8;
            private int queueCapacity = 200;
        }
    }

    @Data
    public static class CacheConfig {
        private Caffeine caffeine = new Caffeine();
        private UserCache user = new UserCache();
        private ConfigCache config = new ConfigCache();

        @Data
        public static class Caffeine {
            private long maximumSize = 10000;
            private long expireAfterWriteMinutes = 10;
            private long expireAfterAccessMinutes = 5;
            private int initialCapacity = 100;
        }

        @Data
        public static class UserCache {
            private long maximumSize = 5000;
            private long expireAfterWriteMinutes = 5;
        }

        @Data
        public static class ConfigCache {
            private long maximumSize = 1000;
            private long expireAfterWriteHours = 1;
        }
    }

    @Data
    public static class TransactionConfig {
        private int defaultTimeout = 30;
        private boolean globalRollbackOnParticipationFailure = false;
    }

}
