package com.mbh.blinddate.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 事务配置属性
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.transaction")
public class TransactionProperties {

    /**
     * 默认事务超时时间(秒)
     */
    private int defaultTimeout = 30;

    /**
     * 参与失败时全局回滚
     */
    private boolean globalRollbackOnParticipationFailure = false;
}
