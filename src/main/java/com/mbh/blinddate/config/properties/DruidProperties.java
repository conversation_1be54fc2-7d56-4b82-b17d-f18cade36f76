package com.mbh.blinddate.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Druid监控配置属性
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.druid")
public class DruidProperties {

    private Monitor monitor = new Monitor();
    private Filter filter = new Filter();

    @Data
    public static class Monitor {
        private String username = "admin";
        private String password = "admin123";
        private String allow = "127.0.0.1";
        private String deny = "";
    }

    @Data
    public static class Filter {
        private String exclusions = "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*,/actuator/*";
    }
}
