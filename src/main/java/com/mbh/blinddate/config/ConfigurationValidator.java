package com.mbh.blinddate.config;

import com.mbh.blinddate.config.properties.AppProperties;
import com.mbh.blinddate.config.properties.AsyncProperties;
import com.mbh.blinddate.config.properties.CacheProperties;
import com.mbh.blinddate.config.properties.TransactionProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 配置验证器
 * 在应用启动后验证配置参数的合理性
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ConfigurationValidator {

    private final AsyncProperties asyncProperties;
    private final CacheProperties cacheProperties;
    private final TransactionProperties transactionProperties;
    private final AppProperties appProperties;

    public ConfigurationValidator(AsyncProperties asyncProperties,
                                CacheProperties cacheProperties,
                                TransactionProperties transactionProperties,
                                AppProperties appProperties) {
        this.asyncProperties = asyncProperties;
        this.cacheProperties = cacheProperties;
        this.transactionProperties = transactionProperties;
        this.appProperties = appProperties;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void validateConfiguration() {
        log.info("开始验证应用配置参数...");
        
        validateAsyncConfiguration();
        validateCacheConfiguration();
        validateTransactionConfiguration();
        
        log.info("配置参数验证完成");
    }

    private void validateAsyncConfiguration() {
        AsyncProperties.TaskPool task = asyncProperties.getTask();
        
        if (task.getCorePoolSize() > task.getMaxPoolSize()) {
            log.warn("异步任务线程池配置警告: core-pool-size({}) > max-pool-size({})", 
                    task.getCorePoolSize(), task.getMaxPoolSize());
        }
        
        if (task.getCorePoolSize() <= 0) {
            log.warn("异步任务线程池配置警告: core-pool-size({}) 应该大于0", task.getCorePoolSize());
        }
        
        if (task.getQueueCapacity() <= 0) {
            log.warn("异步任务线程池配置警告: queue-capacity({}) 应该大于0", task.getQueueCapacity());
        }
        
        log.info("异步任务线程池配置: 核心线程数={}, 最大线程数={}, 队列容量={}", 
                task.getCorePoolSize(), task.getMaxPoolSize(), task.getQueueCapacity());
    }

    private void validateCacheConfiguration() {
        CacheProperties.Caffeine caffeine = cacheProperties.getCaffeine();
        
        if (caffeine.getMaximumSize() <= 0) {
            log.warn("缓存配置警告: maximum-size({}) 应该大于0", caffeine.getMaximumSize());
        }
        
        if (caffeine.getExpireAfterWriteMinutes() <= 0) {
            log.warn("缓存配置警告: expire-after-write-minutes({}) 应该大于0", 
                    caffeine.getExpireAfterWriteMinutes());
        }
        
        log.info("缓存配置: 最大条目数={}, 写入后过期时间={}分钟, 访问后过期时间={}分钟", 
                caffeine.getMaximumSize(), 
                caffeine.getExpireAfterWriteMinutes(),
                caffeine.getExpireAfterAccessMinutes());
    }

    private void validateTransactionConfiguration() {
        int timeout = transactionProperties.getDefaultTimeout();
        
        if (timeout <= 0) {
            log.warn("事务配置警告: default-timeout({}) 应该大于0", timeout);
        }
        
        if (timeout > 300) {
            log.warn("事务配置警告: default-timeout({}) 过长，可能导致长事务问题", timeout);
        }
        
        log.info("事务配置: 默认超时时间={}秒, 全局回滚={}", 
                timeout, transactionProperties.isGlobalRollbackOnParticipationFailure());
    }
}
